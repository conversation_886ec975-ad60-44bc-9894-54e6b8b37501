{"name": "smartcity-ai-traffic-optimization", "version": "1.0.0", "description": "SmartCity AI Traffic Optimization System with Firebase integration", "main": "start.js", "scripts": {"start": "python3 start.py", "dev": "python3 start.py", "firebase:serve": "firebase serve", "firebase:deploy": "firebase deploy", "firebase:emulators": "firebase emulators:start", "firebase:init": "firebase init", "test": "python3 -m pytest", "install-python-deps": "pip3 install -r requirements.txt"}, "keywords": ["smartcity", "traffic", "optimization", "ai", "firebase", "<PERSON><PERSON><PERSON>", "python"], "author": "SmartCity AI Team", "license": "MIT", "dependencies": {"firebase": "^10.7.1", "firebase-admin": "^12.0.0", "firebase-functions": "^4.5.0"}, "devDependencies": {"firebase-tools": "^13.0.0"}, "engines": {"node": ">=16.0.0", "python": ">=3.8.0"}, "repository": {"type": "git", "url": "."}}