#!/bin/bash

# Clean deployment script for SmartCity AI
set -e

echo "🚀 SmartCity AI Deployment to Google Cloud Run"
echo "=============================================="

# Configuration
PROJECT_ID="smart-city-traffic-opt-459903"
SERVICE_NAME="smartcity-ai-backend"
REGION="us-central1"
GCLOUD_PATH="/Users/<USER>/google-cloud-sdk/bin/gcloud"

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running."
    echo "Please start Docker Desktop and try again."
    exit 1
fi

echo "✅ Docker is running"

# Build Docker image
echo "🏗️  Building Docker image..."
docker build -t $SERVICE_NAME .

# Tag for Artifact Registry
echo "🏷️  Tagging image..."
docker tag $SERVICE_NAME us-central1-docker.pkg.dev/$PROJECT_ID/smartcity-repo/$SERVICE_NAME

# Configure Docker authentication for Artifact Registry
echo "🔧 Configuring authentication..."
"$GCLOUD_PATH" auth configure-docker us-central1-docker.pkg.dev --quiet

# Create Artifact Registry repository if it doesn't exist
echo "📦 Creating Artifact Registry repository..."
"$GCLOUD_PATH" artifacts repositories create smartcity-repo \
  --repository-format=docker \
  --location=us-central1 \
  --description="SmartCity AI Docker repository" || true

# Push image
echo "📤 Pushing to Artifact Registry..."
docker push us-central1-docker.pkg.dev/$PROJECT_ID/smartcity-repo/$SERVICE_NAME

# Deploy to Cloud Run
echo "🚀 Deploying to Cloud Run..."
"$GCLOUD_PATH" run deploy $SERVICE_NAME \
  --image us-central1-docker.pkg.dev/$PROJECT_ID/smartcity-repo/$SERVICE_NAME \
  --region $REGION \
  --platform managed \
  --allow-unauthenticated \
  --port 8080 \
  --memory 2Gi \
  --cpu 2 \
  --timeout 900 \
  --max-instances 10 \
  --set-env-vars PORT=8080,ENVIRONMENT=production

# Get service URL
SERVICE_URL=$("$GCLOUD_PATH" run services describe $SERVICE_NAME --region=$REGION --format="value(status.url)")

echo ""
echo "✅ Deployment completed!"
echo "🌐 Backend URL: $SERVICE_URL"
echo "📚 API Docs: $SERVICE_URL/docs"
echo "🔍 Health: $SERVICE_URL/health"
