#!/bin/bash

# SmartCity AI Frontend Deployment Script
# Deploys the frontend to Google Cloud Run

set -e

# Configuration
PROJECT_ID="esoteric-kiln-459609-t9"
SERVICE_NAME="smartcity-ai-frontend"
REGION="us-central1"
IMAGE_NAME="gcr.io/$PROJECT_ID/$SERVICE_NAME"

echo "🚀 Starting SmartCity AI Frontend Deployment to Cloud Run..."

# Check if gcloud is installed
if ! command -v gcloud &> /dev/null; then
    echo "❌ Google Cloud SDK is not installed. Please install it first:"
    echo "   https://cloud.google.com/sdk/docs/install"
    exit 1
fi

# Set the project
echo "📋 Setting project to $PROJECT_ID..."
gcloud config set project $PROJECT_ID

# Enable required APIs
echo "🔧 Enabling required APIs..."
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com

# Build and deploy using Cloud Build
echo "🏗️ Building and deploying frontend container..."
cd frontend

# Deploy to Cloud Run directly
gcloud run deploy $SERVICE_NAME \
    --source . \
    --platform managed \
    --region $REGION \
    --allow-unauthenticated \
    --port 8080 \
    --memory 512Mi \
    --cpu 1 \
    --min-instances 0 \
    --max-instances 10 \
    --set-env-vars "NODE_ENV=production" \
    --quiet

# Get the service URL
SERVICE_URL=$(gcloud run services describe $SERVICE_NAME --platform managed --region $REGION --format 'value(status.url)')

echo ""
echo "✅ Frontend deployment completed successfully!"
echo "🌐 Your SmartCity AI website is now live at:"
echo "   $SERVICE_URL"
echo ""
echo "📊 To view service status:"
echo "   gcloud run services describe $SERVICE_NAME --region $REGION"
echo ""
echo "🔧 To update the website, run this script again"
echo ""
echo "🎉 Happy traffic optimizing!"
