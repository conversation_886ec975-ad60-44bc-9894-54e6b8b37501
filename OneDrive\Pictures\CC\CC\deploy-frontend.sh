#!/bin/bash

# SmartCity AI Frontend Deployment Script
# Deploys the frontend to Google Cloud Storage with CDN

set -e

# Configuration
PROJECT_ID="esoteric-kiln-459609-t9"
BUCKET_NAME="smartcity-ai-frontend"
REGION="us-central1"

echo "🚀 Starting SmartCity AI Frontend Deployment..."

# Check if gcloud is installed
if ! command -v gcloud &> /dev/null; then
    echo "❌ Google Cloud SDK is not installed. Please install it first:"
    echo "   https://cloud.google.com/sdk/docs/install"
    exit 1
fi

# Set the project
echo "📋 Setting project to $PROJECT_ID..."
gcloud config set project $PROJECT_ID

# Create storage bucket if it doesn't exist
echo "🪣 Creating storage bucket..."
if ! gcloud storage buckets describe gs://$BUCKET_NAME &> /dev/null; then
    gcloud storage buckets create gs://$BUCKET_NAME --project=$PROJECT_ID --default-storage-class=STANDARD --location=$REGION
    echo "✅ Bucket created: gs://$BUCKET_NAME"
else
    echo "✅ Bucket already exists: gs://$BUCKET_NAME"
fi

# Configure bucket for website hosting
echo "🌐 Configuring bucket for website hosting..."
gcloud storage buckets update gs://$BUCKET_NAME --web-main-page-suffix=index.html --web-error-page=404.html

# Make bucket publicly readable
echo "🔓 Making bucket publicly readable..."
gcloud storage buckets add-iam-policy-binding gs://$BUCKET_NAME --member=allUsers --role=roles/storage.objectViewer

# Copy frontend files to bucket
echo "📁 Uploading frontend files..."
gcloud storage rsync ./frontend/ gs://$BUCKET_NAME/ --recursive --delete-unmatched-destination-objects

# Set proper content types and cache control
echo "🏷️ Setting content types and cache control..."
gcloud storage objects update gs://$BUCKET_NAME/*.html --content-type="text/html" --cache-control="public, max-age=300"
gcloud storage objects update gs://$BUCKET_NAME/static/css/*.css --content-type="text/css" --cache-control="public, max-age=3600"
gcloud storage objects update gs://$BUCKET_NAME/static/js/*.js --content-type="application/javascript" --cache-control="public, max-age=3600"

# Get the website URL
WEBSITE_URL="https://storage.googleapis.com/$BUCKET_NAME/index.html"

echo ""
echo "✅ Frontend deployment completed successfully!"
echo "🌐 Your SmartCity AI website is now live at:"
echo "   $WEBSITE_URL"
echo ""
echo "📊 To view deployment status:"
echo "   gsutil ls -L gs://$BUCKET_NAME"
echo ""
echo "🔧 To update the website, run this script again"
echo ""
echo "🎉 Happy traffic optimizing!"
