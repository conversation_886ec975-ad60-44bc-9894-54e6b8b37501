/* Main Application Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #f8fafc;
    color: #2d3748;
    line-height: 1.6;
}

/* Ensure content is visible by default */
.main, .header {
    display: block !important;
    opacity: 1 !important;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Header */
.header {
    background: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 0;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    text-decoration: none;
    color: #2d3748;
}

.logo-icon {
    font-size: 1.5rem;
    color: #667eea;
}

.brand-text span {
    font-size: 1.25rem;
    font-weight: 700;
    display: block;
}

.brand-text small {
    font-size: 0.75rem;
    color: #718096;
    font-weight: 400;
}

.nav {
    display: flex;
    gap: 2rem;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    text-decoration: none;
    color: #718096;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-weight: 500;
}

.nav-link:hover,
.nav-link.active {
    color: #667eea;
    background: rgba(102, 126, 234, 0.1);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.auth-buttons {
    display: flex;
    gap: 0.5rem;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem;
    background: #f7fafc;
    border-radius: 10px;
}

.user-avatar img {
    width: 32px;
    height: 32px;
    border-radius: 50%;
}

.user-details {
    display: flex;
    flex-direction: column;
}

.user-name {
    font-weight: 600;
    font-size: 0.9rem;
}

.user-email {
    font-size: 0.75rem;
    color: #718096;
}

/* Main Content */
.main {
    padding: 2rem 0;
}

.section {
    display: none;
}

.section.active {
    display: block;
}

.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.section-header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.section-header p {
    font-size: 1.1rem;
    color: #718096;
}

/* Route Planner */
.route-planner {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.route-form {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    height: fit-content;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #2d3748;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-options {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.option-group {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.option-group label {
    font-weight: 600;
    margin-bottom: 0;
}

.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.checkbox-wrapper {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
}

/* Map Container */
.map-container {
    position: relative;
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.map {
    width: 100%;
    height: 400px;
}

.map-loading {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: white;
    color: #718096;
}

.map-loading i {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: #667eea;
}

/* Route Results */
.route-results {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.results-header h3 {
    color: #2d3748;
    font-size: 1.5rem;
}

.route-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.summary-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f7fafc;
    border-radius: 10px;
}

.summary-icon {
    font-size: 1.5rem;
    color: #667eea;
}

.summary-details {
    display: flex;
    flex-direction: column;
}

.summary-label {
    font-size: 0.85rem;
    color: #718096;
    font-weight: 500;
}

.summary-value {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2d3748;
}

/* Eco Metrics */
.eco-summary {
    padding: 1.5rem;
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    border-radius: 10px;
    color: white;
}

.eco-summary h4 {
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.eco-items {
    display: flex;
    gap: 2rem;
}

.eco-item {
    display: flex;
    flex-direction: column;
}

.eco-label {
    font-size: 0.85rem;
    opacity: 0.9;
}

.eco-value {
    font-size: 1.2rem;
    font-weight: 600;
}

.eco-score-excellent { color: #48bb78; }
.eco-score-good { color: #38a169; }
.eco-score-fair { color: #ed8936; }
.eco-score-poor { color: #e53e3e; }

/* Chat Interface */
.chat-container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    height: 500px;
    display: flex;
    flex-direction: column;
    margin-bottom: 2rem;
}

.chat-messages {
    flex: 1;
    padding: 1.5rem;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.chat-message {
    display: flex;
    gap: 0.75rem;
    align-items: flex-start;
}

.chat-message.user {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.chat-message.bot .message-avatar {
    background: #667eea;
    color: white;
}

.chat-message.user .message-avatar {
    background: #48bb78;
    color: white;
}

.message-content {
    max-width: 70%;
    padding: 0.75rem 1rem;
    border-radius: 15px;
    background: #f7fafc;
}

.chat-message.user .message-content {
    background: #667eea;
    color: white;
}

.chat-input-container {
    padding: 1rem 1.5rem;
    border-top: 1px solid #e2e8f0;
}

.chat-input {
    display: flex;
    gap: 0.5rem;
}

.chat-input input {
    flex: 1;
    padding: 0.75rem;
    border: 2px solid #e2e8f0;
    border-radius: 25px;
    font-size: 1rem;
}

.chat-input input:focus {
    outline: none;
    border-color: #667eea;
}

/* Eco Tips */
.eco-tips {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.eco-tips h3 {
    margin-bottom: 1.5rem;
    color: #2d3748;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.tips-container {
    display: grid;
    gap: 1.5rem;
}

.tip-item {
    background: #f7fafc;
    border-radius: 12px;
    padding: 1.5rem;
    border-left: 4px solid #48bb78;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.tip-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    background: #edf2f7;
}

.tip-item.high-impact {
    border-left-color: #48bb78;
    background: linear-gradient(135deg, #f0fff4 0%, #f7fafc 100%);
}

.tip-item.medium-impact {
    border-left-color: #ed8936;
    background: linear-gradient(135deg, #fffaf0 0%, #f7fafc 100%);
}

.tip-item.low-impact {
    border-left-color: #667eea;
    background: linear-gradient(135deg, #f7faff 0%, #f7fafc 100%);
}

.tip-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.tip-icon {
    font-size: 1.5rem;
    margin-right: 0.5rem;
}

.tip-category {
    font-weight: 600;
    color: #2d3748;
    font-size: 0.9rem;
    flex: 1;
}

.tip-impact-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.tip-content {
    color: #4a5568;
    line-height: 1.6;
    margin-bottom: 1rem;
    font-size: 0.95rem;
}

.tip-savings {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    background: rgba(72, 187, 120, 0.1);
    border-radius: 8px;
    color: #22543d;
    font-size: 0.85rem;
    font-weight: 500;
}

.tip-savings i {
    color: #48bb78;
}

.impact-high {
    background: #c6f6d5;
    color: #22543d;
}

.impact-medium {
    background: #feebc8;
    color: #c05621;
}

.impact-low {
    background: #e2e8f0;
    color: #4a5568;
}

/* Buttons */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 0.95rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

.btn-outline {
    background: transparent;
    border: 2px solid #e2e8f0;
    color: #2d3748;
}

.btn-outline:hover {
    background: #f7fafc;
    border-color: #cbd5e0;
}

.btn-full {
    width: 100%;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
}

/* Search History */
.search-history {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.no-history {
    text-align: center;
    padding: 3rem;
    color: #718096;
}

.no-history i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #cbd5e0;
}

/* Loading and Notifications */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-spinner {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    color: #2d3748;
}

.loading-spinner i {
    font-size: 2rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.notification-container {
    position: fixed;
    top: 2rem;
    right: 2rem;
    z-index: 1001;
}

.notification {
    background: white;
    padding: 1rem 1.5rem;
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    border-left: 4px solid #667eea;
    animation: slideIn 0.3s ease;
}

.notification.success { border-left-color: #48bb78; }
.notification.error { border-left-color: #f56565; }
.notification.warning { border-left-color: #ed8936; }

@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

/* Search History Styles */
.history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e2e8f0;
}

.history-header h3 {
    color: #2d3748;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.history-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.history-item {
    background: #f7fafc;
    border-radius: 12px;
    padding: 1.5rem;
    border-left: 4px solid #667eea;
    transition: all 0.3s ease;
}

.history-item:hover {
    background: #edf2f7;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.history-item.new-item {
    background: linear-gradient(135deg, #c6f6d5 0%, #f0fff4 100%);
    border-left-color: #48bb78;
    animation: newItemPulse 2s ease-in-out;
}

@keyframes newItemPulse {
    0% {
        background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
        color: white;
        transform: scale(1.02);
    }
    50% {
        background: linear-gradient(135deg, #c6f6d5 0%, #f0fff4 100%);
        color: inherit;
        transform: scale(1);
    }
    100% {
        background: #f7fafc;
        color: inherit;
        transform: scale(1);
    }
}

.history-main {
    margin-bottom: 1rem;
}

.route-points {
    margin-bottom: 1rem;
}

.route-point {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.route-point.start i {
    color: #48bb78;
}

.route-point.end i {
    color: #f56565;
}

.route-arrow {
    margin-left: 0.5rem;
    color: #a0aec0;
}

.history-details {
    display: flex;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: #4a5568;
}

.detail-item.eco {
    color: #38a169;
    font-weight: 600;
}

.detail-item.eco-score {
    color: #667eea;
    font-weight: 600;
}

.detail-item i.fa-car {
    color: #4299e1;
}

.history-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 1rem;
    border-top: 1px solid #e2e8f0;
}

.history-timestamp {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.85rem;
    color: #718096;
}

.history-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-icon {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 6px;
    background: transparent;
    color: #718096;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.btn-icon:hover {
    background: #e2e8f0;
    color: #2d3748;
}

.btn-icon.repeat-search:hover {
    background: #c6f6d5;
    color: #22543d;
}

.btn-icon.delete-search:hover {
    background: #fed7d7;
    color: #c53030;
}

/* Typing Indicator */
.typing-indicator .typing-dots {
    display: flex;
    gap: 0.25rem;
    align-items: center;
}

.typing-dots span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #a0aec0;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
    40% { transform: scale(1); opacity: 1; }
}

/* Welcome Notification */
.welcome-notification {
    position: fixed;
    top: 2rem;
    right: 2rem;
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(72, 187, 120, 0.3);
    z-index: 1001;
    animation: slideIn 0.3s ease;
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

/* Enhanced Button Styles */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.btn:active {
    transform: translateY(0);
}

/* Form Enhancements */
.form-group input:invalid {
    border-color: #f56565;
}

.form-group input:valid {
    border-color: #48bb78;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
    }

    .nav {
        gap: 1rem;
        flex-wrap: wrap;
        justify-content: center;
    }

    .route-planner {
        grid-template-columns: 1fr;
    }

    .route-summary {
        grid-template-columns: 1fr;
    }

    .eco-items {
        flex-direction: column;
        gap: 1rem;
    }

    .history-details {
        flex-direction: column;
        gap: 0.75rem;
    }

    .history-meta {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }

    .section-header h1 {
        font-size: 2rem;
    }

    .user-info {
        flex-direction: column;
        text-align: center;
    }
}
