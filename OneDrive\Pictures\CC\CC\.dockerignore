# Git
.git
.gitignore

# Documentation
README.md
*.md

# Environment files (except production)
.env
.env.local
.env.development

# Node modules and frontend build files
node_modules/
frontend/
*.log

# Python cache
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
*.so
.pytest_cache/

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Test files
tests/
test_*.py
*_test.py

# Development files
start.py
start.sh
start.bat
main_simple.py
app.py
deploy-*.sh
test-*.sh
*.md

# Migration and setup files
migrate-to-new-firebase.sh
firebase-setup.sh
validate_migration.py
test_firebase_connection.py
FIREBASE_MIGRATION_GUIDE.md
MIGRATION_CHECKLIST.md
FIREBASE_SETUP_GUIDE.md

# Firebase files (will be handled separately)
firebase.json
firestore.rules
firestore.indexes.json
.firebaserc
