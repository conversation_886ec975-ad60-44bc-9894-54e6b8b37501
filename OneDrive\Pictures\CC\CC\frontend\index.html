<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SmartCity AI - Traffic Optimization System</title>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="static/css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Ctext y='.9em' font-size='90'%3E🚦%3C/text%3E%3C/svg%3E">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <div class="logo-icon">
                        <i class="fas fa-traffic-light"></i>
                    </div>
                    <div class="brand-text">
                        <span>SmartCity AI</span>
                        <small>Traffic Optimization</small>
                    </div>
                </div>
                
                <nav class="nav">
                    <a href="#" class="nav-link active" data-section="route">
                        <i class="fas fa-route"></i> Route Planning
                    </a>
                    <a href="#" class="nav-link" data-section="chat">
                        <i class="fas fa-comments"></i> AI Assistant
                    </a>
                    <a href="#" class="nav-link" data-section="history">
                        <i class="fas fa-history"></i> History
                    </a>
                </nav>
                
                <div class="header-actions">
                    <div id="auth-buttons" class="auth-buttons">
                        <a href="/auth.html" class="btn btn-outline btn-sm">
                            <i class="fas fa-sign-in-alt"></i> Sign In
                        </a>
                    </div>
                    
                    <div id="user-info" class="user-info" style="display: none;">
                        <!-- User info will be populated by auth.js -->
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <!-- Route Planning Section -->
            <section id="route-section" class="section active">
                <div class="section-header">
                    <h1>Smart Route Planning</h1>
                    <p>Get optimized routes with real-time traffic data and eco-friendly options</p>
                </div>
                
                <div class="route-planner">
                    <div class="route-form">
                        <div class="form-group">
                            <label for="source">
                                <i class="fas fa-circle"></i> Starting Point
                            </label>
                            <input type="text" id="source" placeholder="Enter starting location" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="destination">
                                <i class="fas fa-map-marker-alt"></i> Destination
                            </label>
                            <input type="text" id="destination" placeholder="Enter destination" required>
                        </div>
                        
                        <div class="form-options">
                            <div class="option-group">
                                <label for="route-type">Route Type:</label>
                                <select id="route-type">
                                    <option value="fastest">Fastest Route</option>
                                    <option value="shortest">Shortest Route</option>
                                    <option value="eco_friendly">Eco-Friendly Route</option>
                                </select>
                            </div>

                            <div class="option-group">
                                <label for="vehicle-type">Vehicle Type:</label>
                                <select id="vehicle-type">
                                    <option value="car">Car</option>
                                    <option value="motorcycle">Motorcycle</option>
                                    <option value="bicycle">Bicycle</option>
                                    <option value="electric_car">Electric Car</option>
                                    <option value="hybrid">Hybrid Car</option>
                                </select>
                            </div>

                            <div class="checkbox-group">
                                <label class="checkbox-wrapper">
                                    <input type="checkbox" id="avoid-tolls">
                                    <span class="checkmark"></span>
                                    Avoid Tolls
                                </label>
                                <label class="checkbox-wrapper">
                                    <input type="checkbox" id="avoid-highways">
                                    <span class="checkmark"></span>
                                    Avoid Highways
                                </label>
                            </div>
                        </div>
                        
                        <button id="get-route-btn" class="btn btn-primary btn-full">
                            <i class="fas fa-search"></i> Get Route
                        </button>
                    </div>
                    
                    <div class="map-container">
                        <div id="map" class="map"></div>
                        <div class="map-loading" id="map-loading">
                            <i class="fas fa-spinner fa-spin"></i>
                            <p>Loading map...</p>
                        </div>
                    </div>
                </div>
                
                <!-- Route Results -->
                <div id="route-results" class="route-results" style="display: none;">
                    <div class="results-header">
                        <h3>Route Information</h3>
                        <div class="results-actions">
                            <button id="save-route-btn" class="btn btn-outline btn-sm">
                                <i class="fas fa-save"></i> Save Route
                            </button>
                        </div>
                    </div>
                    <div id="route-info" class="route-info">
                        <!-- Route details will be populated by JavaScript -->
                    </div>
                    <div id="eco-metrics" class="eco-metrics">
                        <!-- Eco metrics will be populated by JavaScript -->
                    </div>
                </div>
            </section>

            <!-- AI Assistant Section -->
            <section id="chat-section" class="section">
                <div class="section-header">
                    <h1>AI Assistant</h1>
                    <p>Get personalized eco-friendly transportation advice</p>
                </div>
                
                <div class="chat-container">
                    <div class="chat-messages" id="chat-messages">
                        <div class="chat-message bot">
                            <div class="message-avatar">
                                <i class="fas fa-robot"></i>
                            </div>
                            <div class="message-content">
                                <p>Hello! I'm your eco-friendly transportation assistant. Ask me about routes, environmental impact, or sustainable travel options!</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="chat-input-container">
                        <div class="chat-input">
                            <input type="text" id="chat-input" placeholder="Ask about eco-friendly routes, traffic conditions, or environmental tips...">
                            <button id="send-chat-btn" class="btn btn-primary">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Eco Tips -->
                <div class="eco-tips">
                    <h3><i class="fas fa-leaf"></i> Eco Tips</h3>
                    <div id="eco-tips-container" class="tips-container">
                        <!-- Tips will be loaded by JavaScript -->
                    </div>
                </div>
            </section>

            <!-- Search History Section -->
            <section id="history-section" class="section">
                <div class="section-header">
                    <h1>Search History</h1>
                    <p>View and manage your previous route searches</p>
                </div>
                
                <div id="search-history" class="search-history">
                    <!-- Search history will be populated by JavaScript -->
                </div>
            </section>
        </div>
    </main>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay" style="display: none;">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <p>Processing...</p>
        </div>
    </div>

    <!-- Notification Container -->
    <div id="notification-container" class="notification-container"></div>

    <!-- Firebase Scripts -->
    <script type="module" src="static/js/firebase-config.js"></script>
    <script type="module" src="static/js/auth.js"></script>
    <script type="module" src="static/js/search-history.js"></script>

    <!-- Main Application Script -->
    <script type="module" src="static/js/main.js"></script>

    <!-- Authentication Check and Google Maps API Loading -->
    <script type="module">
        import authManager from 'static/js/auth.js';

        // Check authentication status on page load
        document.addEventListener('DOMContentLoaded', () => {
            console.log('Page loaded, checking authentication...');

            // Simple auth check without complex loading screens
            let authCheckComplete = false;

            // Listen for auth state changes
            authManager.addAuthStateListener((user) => {
                if (authCheckComplete) return; // Prevent multiple triggers
                authCheckComplete = true;

                if (user) {
                    // User is authenticated, show main app
                    console.log('User authenticated:', user.email);
                    showMainApplication();
                    loadGoogleMapsAPI();
                } else {
                    // User not authenticated, redirect to auth page
                    console.log('User not authenticated, redirecting to auth page');
                    window.location.href = '/auth.html';
                }
            });

            // Fallback check after 2 seconds
            setTimeout(() => {
                if (!authCheckComplete) {
                    const currentUser = authManager.getCurrentUser();
                    if (!currentUser) {
                        console.log('Auth check timeout, redirecting to auth page');
                        window.location.href = '/auth.html';
                    } else {
                        console.log('Auth check timeout but user found:', currentUser.email);
                        showMainApplication();
                        loadGoogleMapsAPI();
                    }
                    authCheckComplete = true;
                }
            }, 2000);

            // Emergency fallback - show dashboard after 3 seconds regardless
            setTimeout(() => {
                console.log('Emergency fallback - showing dashboard');
                showMainApplication();
                loadGoogleMapsAPI();
            }, 3000);
        });

        function showMainApplication() {
            // Ensure main content is visible
            const mainContent = document.querySelector('.main');
            const header = document.querySelector('.header');

            if (mainContent) {
                mainContent.style.display = 'block';
                mainContent.style.opacity = '1';
            }
            if (header) {
                header.style.display = 'block';
                header.style.opacity = '1';
            }

            console.log('Main application displayed');
        }

        // Load Google Maps API dynamically with the correct API key
        async function loadGoogleMapsAPI() {
            try {
                const response = await fetch('https://smartcity-ai-backend-403253346250.asia-south1.run.app/api/config');
                const config = await response.json();

                if (!config.google_maps_api_key) {
                    console.error('Google Maps API key not configured');
                    const mapLoading = document.getElementById('map-loading');
                    if (mapLoading) {
                        mapLoading.innerHTML = `
                            <i class="fas fa-exclamation-triangle"></i>
                            <p>Google Maps API key not configured</p>
                        `;
                    }
                    return;
                }

                const script = document.createElement('script');
                script.src = `https://maps.googleapis.com/maps/api/js?key=${config.google_maps_api_key}&libraries=places,geometry&callback=initMap`;
                script.async = true;
                script.defer = true;
                document.head.appendChild(script);
            } catch (error) {
                console.error('Error loading Google Maps API:', error);
                const mapLoading = document.getElementById('map-loading');
                if (mapLoading) {
                    mapLoading.innerHTML = `
                        <i class="fas fa-exclamation-triangle"></i>
                        <p>Error loading map</p>
                    `;
                }
            }
        }

        // Initialize Google Maps
        window.initMap = function() {
            if (window.trafficApp && window.trafficApp.initializeMap) {
                window.trafficApp.initializeMap();
            }
        };
    </script>
</body>
</html>
