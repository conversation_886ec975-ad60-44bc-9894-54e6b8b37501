steps:
  # Build the container image
  - name: 'gcr.io/cloud-builders/docker'
    args: ['build', '-t', 'gcr.io/$PROJECT_ID/smartcity-ai:$COMMIT_SHA', '.']
  
  # Push the container image to Container Registry
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/smartcity-ai:$COMMIT_SHA']
  
  # Deploy container image to Cloud Run
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: gcloud
    args:
      - 'run'
      - 'deploy'
      - 'smartcity-ai'
      - '--image'
      - 'gcr.io/$PROJECT_ID/smartcity-ai:$COMMIT_SHA'
      - '--region'
      - 'us-central1'
      - '--platform'
      - 'managed'
      - '--allow-unauthenticated'
      - '--port'
      - '8080'
      - '--memory'
      - '2Gi'
      - '--cpu'
      - '2'
      - '--max-instances'
      - '10'
      - '--set-env-vars'
      - 'GOOGLE_CLOUD_PROJECT_ID=esoteric-kiln-459609-t9,DEBUG=False,HOST=0.0.0.0,PORT=8080,GOOGLE_MAPS_API_KEY=AIzaSyDdGyIXH74Bk53kKtMyC4dCnOyVdYy8hM4,GEMINI_API_KEY=AIzaSyAWvkbvnuLGXFLiuVkaUg-l0ommE7x66Io,OPENWEATHER_API_KEY=********************************,TOMTOM_API_KEY=********************************,WEATHERAPI_KEY=6b925b6c91f34642bb2192752251806,TRANSITLAND_API_KEY=qC1rRXxkU4oqIY0ONplbDbfqi3h9WfQ4'

images:
  - 'gcr.io/$PROJECT_ID/smartcity-ai:$COMMIT_SHA'
