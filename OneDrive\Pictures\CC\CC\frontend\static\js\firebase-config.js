// Firebase Configuration and Initialization
import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
import { getAuth, connectAuthEmulator } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';
import { getFirestore, connectFirestoreEmulator } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';
import { getAnalytics } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-analytics.js';

// Your web app's Firebase configuration
// TODO: Replace this with YOUR Firebase project configuration
// Get this from: Firebase Console > Project Settings > General > Your apps > Web app
const firebaseConfig = {
  apiKey: "AIzaSyDsBFGpYsnNrIv0owBnbueYd5DMB8jfh5k",
  authDomain: "smart-traffic-511c5.firebaseapp.com",
  projectId: "smart-traffic-511c5",
  storageBucket: "smart-traffic-511c5.firebasestorage.app",
  messagingSenderId: "810928629643",
  appId: "1:810928629643:web:589476964df58011af718e",
  measurementId: "G-JGKYJZ46EB" // Optional, only if you enabled Analytics
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase services
const auth = getAuth(app);
const db = getFirestore(app);
const analytics = getAnalytics(app);

// Export Firebase services for use in other modules
export { auth, db, analytics };

// Global Firebase app instance for backward compatibility
window.firebaseApp = app;
window.firebaseAuth = auth;
window.firebaseDb = db;

console.log('Firebase initialized successfully');
