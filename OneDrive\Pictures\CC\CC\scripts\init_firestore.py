#!/usr/bin/env python3
"""
Firestore Database Initialization Script for SmartCity AI
Sets up collections, indexes, and sample data
"""

import os
import sys
import json
from datetime import datetime, timedelta
from google.cloud import firestore
from google.oauth2 import service_account

def init_firestore():
    """Initialize Firestore database with collections and sample data"""
    
    # Initialize Firestore client
    try:
        # Try to use service account key if available
        if os.path.exists('service-account-key.json'):
            credentials = service_account.Credentials.from_service_account_file(
                'service-account-key.json'
            )
            db = firestore.Client(credentials=credentials)
        else:
            # Use default credentials (for Cloud Shell or local gcloud auth)
            db = firestore.Client()
        
        print("✅ Connected to Firestore successfully!")
        
        # Create collections and sample data
        setup_traffic_data(db)
        setup_user_preferences(db)
        setup_route_history(db)
        setup_chat_history(db)
        setup_system_config(db)
        
        print("🎉 Firestore database initialized successfully!")
        
    except Exception as e:
        print(f"❌ Error initializing Firestore: {e}")
        sys.exit(1)

def setup_traffic_data(db):
    """Set up traffic data collection with sample data"""
    print("📊 Setting up traffic_data collection...")
    
    traffic_ref = db.collection('traffic_data')
    
    # Sample traffic data for Mumbai locations
    sample_data = [
        {
            'location': 'Bandra-Kurla Complex',
            'coordinates': {'lat': 19.0596, 'lng': 72.8656},
            'traffic_level': 'high',
            'congestion_score': 8.5,
            'average_speed': 15.2,
            'timestamp': datetime.now(),
            'weather_condition': 'clear',
            'rush_hour': True
        },
        {
            'location': 'Andheri East',
            'coordinates': {'lat': 19.1136, 'lng': 72.8697},
            'traffic_level': 'medium',
            'congestion_score': 6.2,
            'average_speed': 25.8,
            'timestamp': datetime.now(),
            'weather_condition': 'partly_cloudy',
            'rush_hour': True
        },
        {
            'location': 'Powai',
            'coordinates': {'lat': 19.1176, 'lng': 72.9060},
            'traffic_level': 'low',
            'congestion_score': 3.1,
            'average_speed': 45.5,
            'timestamp': datetime.now(),
            'weather_condition': 'clear',
            'rush_hour': False
        }
    ]
    
    for i, data in enumerate(sample_data):
        traffic_ref.document(f'sample_{i+1}').set(data)
    
    print(f"   ✅ Added {len(sample_data)} traffic data samples")

def setup_user_preferences(db):
    """Set up user preferences collection"""
    print("👤 Setting up user_preferences collection...")
    
    prefs_ref = db.collection('user_preferences')
    
    # Sample user preferences
    sample_prefs = {
        'default_user': {
            'preferred_route_type': 'fastest',
            'avoid_tolls': False,
            'avoid_highways': False,
            'eco_mode': True,
            'notifications_enabled': True,
            'language': 'en',
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        }
    }
    
    for user_id, prefs in sample_prefs.items():
        prefs_ref.document(user_id).set(prefs)
    
    print(f"   ✅ Added {len(sample_prefs)} user preference samples")

def setup_route_history(db):
    """Set up route history collection"""
    print("🛣️ Setting up route_history collection...")
    
    history_ref = db.collection('route_history')
    
    # Sample route history
    sample_routes = [
        {
            'user_id': 'default_user',
            'origin': {'lat': 19.0760, 'lng': 72.8777, 'address': 'Mumbai Central'},
            'destination': {'lat': 19.0596, 'lng': 72.8656, 'address': 'BKC'},
            'route_type': 'fastest',
            'distance_km': 12.5,
            'duration_minutes': 35,
            'fuel_saved_ml': 150,
            'co2_saved_g': 45,
            'timestamp': datetime.now() - timedelta(hours=2),
            'weather': 'clear'
        },
        {
            'user_id': 'default_user',
            'origin': {'lat': 19.0596, 'lng': 72.8656, 'address': 'BKC'},
            'destination': {'lat': 19.1136, 'lng': 72.8697, 'address': 'Andheri East'},
            'route_type': 'eco_friendly',
            'distance_km': 8.2,
            'duration_minutes': 28,
            'fuel_saved_ml': 200,
            'co2_saved_g': 60,
            'timestamp': datetime.now() - timedelta(hours=5),
            'weather': 'rainy'
        }
    ]
    
    for i, route in enumerate(sample_routes):
        history_ref.document(f'route_{i+1}').set(route)
    
    print(f"   ✅ Added {len(sample_routes)} route history samples")

def setup_chat_history(db):
    """Set up chat history collection"""
    print("💬 Setting up chat_history collection...")
    
    chat_ref = db.collection('chat_history')
    
    # Sample chat history
    sample_chats = [
        {
            'user_id': 'default_user',
            'session_id': 'session_001',
            'messages': [
                {
                    'role': 'user',
                    'content': 'What is the best route from Mumbai Central to BKC?',
                    'timestamp': datetime.now() - timedelta(minutes=10)
                },
                {
                    'role': 'assistant',
                    'content': 'Based on current traffic conditions, I recommend taking the Western Express Highway route. It will take approximately 35 minutes and save you 150ml of fuel compared to other routes.',
                    'timestamp': datetime.now() - timedelta(minutes=9)
                }
            ],
            'created_at': datetime.now() - timedelta(minutes=10),
            'updated_at': datetime.now() - timedelta(minutes=9)
        }
    ]
    
    for i, chat in enumerate(sample_chats):
        chat_ref.document(f'chat_{i+1}').set(chat)
    
    print(f"   ✅ Added {len(sample_chats)} chat history samples")

def setup_system_config(db):
    """Set up system configuration collection"""
    print("⚙️ Setting up system_config collection...")
    
    config_ref = db.collection('system_config')
    
    # System configuration
    system_config = {
        'app_version': '2.0.0',
        'api_version': 'v1',
        'maintenance_mode': False,
        'features': {
            'route_optimization': True,
            'eco_chatbot': True,
            'real_time_traffic': True,
            'weather_integration': True
        },
        'limits': {
            'max_routes_per_day': 100,
            'max_chat_messages_per_session': 50
        },
        'updated_at': datetime.now()
    }
    
    config_ref.document('app_config').set(system_config)
    
    print("   ✅ Added system configuration")

if __name__ == "__main__":
    print("🚀 Initializing SmartCity AI Firestore Database...")
    init_firestore()
