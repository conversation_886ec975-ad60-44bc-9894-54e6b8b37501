# SmartCity AI - Traffic Optimization System

A comprehensive web-based traffic optimization system that uses AI/ML for route planning, Google Maps integration for real-time routing, and eco-friendly route recommendations with real-time data from multiple APIs.

## 🚀 Features

### Core Functionality
- **Real-time Route Planning**: Get optimized routes using Google Maps Directions API
- **AI-Powered Eco Assistant**: Gemini 1.5 Flash powered chatbot for environmental advice
- **User Authentication**: Firebase Auth with email/password and Google signin
- **Search History**: Firebase Firestore integration for persistent search history
- **Real-time Data Integration**: Weather, traffic, transit, and air quality data

### Technical Features
- **FastAPI Backend**: High-performance async API with automatic documentation
- **Firebase Integration**: Authentication and Firestore database
- **Google Maps Integration**: Interactive maps with traffic layer and custom controls
- **Real-time APIs**: WeatherAPI, TomTom Traffic, Transitland, OpenWeather
- **Responsive Frontend**: Modern HTML/CSS/JS with mobile support

## 🏗️ Architecture

```
Frontend (HTML/CSS/JS)
    ↓
FastAPI Backend
    ├── Google Maps API (Routes, Traffic)
    ├── Gemini AI (Eco Assistant)
    ├── Real Data APIs (Weather, Traffic, Transit)
    └── Firebase (Auth, Firestore)
```

## 📋 Prerequisites

- Python 3.8+
- Node.js (for frontend dependencies, optional)
- Google Maps API key
- Google Gemini AI API key
- Firebase project setup
- API keys for real data services

## 🛠️ Installation

### 1. Clone the Repository
```bash
git clone <repository-url>
cd SmartCity-AI
```

### 2. Install Python Dependencies
```bash
pip install -r requirements.txt
```

### 3. Configure Environment Variables
Copy `.env.example` to `.env` and update with your API keys:

```env
# Google APIs
GOOGLE_MAPS_API_KEY=your_google_maps_api_key
GEMINI_API_KEY=your_gemini_api_key

# Real Data APIs
OPENWEATHER_API_KEY=********************************
TOMTOM_API_KEY=********************************
WEATHERAPI_KEY=6b925b6c91f34642bb2192752251806
TRANSITLAND_API_KEY=qC1rRXxkU4oqIY0ONplbDbfqi3h9WfQ4

# Application
DEBUG=True
HOST=0.0.0.0
PORT=8000
```

### 4. Firebase Setup
1. Create a Firebase project at https://console.firebase.google.com/
2. Enable Authentication (Email/Password and Google)
3. Create a Firestore database
4. Update `frontend/static/js/firebase-config.js` with your Firebase config

### 5. Run the Application
```bash
python -m backend.main
```

The application will be available at http://localhost:8000

## 🎯 Usage Guide

### Authentication
1. Visit http://localhost:8000/auth.html to sign up or sign in
2. Use email/password or Google signin
3. User data is stored in Firebase Firestore

### Route Planning
1. Enter source and destination addresses
2. Select route type (Fastest, Shortest, or Eco-friendly)
3. Configure options (avoid tolls/highways)
4. Click "Get Route" to see optimized path
5. View route details including distance, duration, and eco metrics

### AI Assistant
1. Navigate to the AI Assistant tab
2. Ask questions about routes, environmental impact, or transportation
3. Get personalized eco-friendly advice powered by Gemini AI

### Search History
1. Sign in to automatically save your route searches
2. View previous searches in the History tab
3. Repeat or delete previous searches
4. Data syncs across all your devices

## 🔧 API Endpoints

### Route Planning
- `POST /api/route` - Calculate optimized routes
- `GET /api/weather` - Get weather and air quality data
- `GET /api/traffic` - Get real-time traffic information
- `GET /api/transit` - Get public transit data

### AI Assistant
- `POST /api/eco_chat` - Chat with the eco assistant
- `GET /api/eco_tips` - Get personalized eco tips

### Configuration
- `GET /api/config` - Get application configuration
- `GET /health` - Health check endpoint

## 🌍 Real Data Sources

- **Weather**: WeatherAPI for current conditions and forecasts
- **Traffic**: TomTom Traffic API for real-time traffic flow and incidents
- **Transit**: Transitland API for public transportation data
- **Air Quality**: OpenWeather Air Pollution API

## 🔒 Security Features

- Firebase Authentication with secure token management
- CORS protection for API endpoints
- Input validation and sanitization
- Secure API key management through environment variables

## 📱 Responsive Design

The application is fully responsive and works on:
- Desktop computers
- Tablets
- Mobile phones
- All modern web browsers

## 🧪 Testing

### Run the Application
```bash
python -m backend.main
```

### Test API Endpoints
Visit http://localhost:8000/docs for interactive API documentation.

### Frontend Testing
1. Open http://localhost:8000 in your browser
2. Test authentication flow
3. Test route planning with real addresses
4. Verify map functionality and AI assistant

## 🚀 Deployment

### Quick Deploy Options

#### Railway (Recommended - Free Tier Available)
[![Deploy on Railway](https://railway.app/button.svg)](https://railway.app/template/smartcity-ai)

#### Google Cloud Run
```bash
./deploy_cloudrun.sh
```

#### Manual Deployment
See [DEPLOYMENT.md](DEPLOYMENT.md) for detailed deployment instructions.

### Local Development
```bash
python -m backend.main
```

### Production Deployment
1. Set `DEBUG=False` in environment variables
2. Configure production Firebase settings
3. Use a production WSGI server like Gunicorn:
```bash
gunicorn backend.main:app -w 4 -k uvicorn.workers.UvicornWorker
```

## 📄 License

This project is licensed under the MIT License.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📞 Support

For support and questions:
- Check the API documentation at `/docs`
- Review the code comments and documentation
- Open an issue on the repository

## 🔄 Version History

- **v2.0.0** - Complete rewrite with real data integration
- **v1.0.0** - Initial release with basic functionality
