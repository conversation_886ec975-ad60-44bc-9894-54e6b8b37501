<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Page Not Found - SmartCity AI</title>
    <link rel="stylesheet" href="static/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .error-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            text-align: center;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .error-icon {
            font-size: 6rem;
            margin-bottom: 2rem;
            opacity: 0.8;
        }
        .error-title {
            font-size: 3rem;
            margin-bottom: 1rem;
            font-weight: 300;
        }
        .error-message {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            max-width: 600px;
        }
        .error-actions {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            justify-content: center;
        }
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        .btn-primary {
            background-color: #4CAF50;
            color: white;
        }
        .btn-secondary {
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }
        @media (max-width: 768px) {
            .error-title {
                font-size: 2rem;
            }
            .error-icon {
                font-size: 4rem;
            }
            .error-actions {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon">
            <i class="fas fa-map-marked-alt"></i>
        </div>
        <h1 class="error-title">404 - Route Not Found</h1>
        <p class="error-message">
            Looks like you've taken a wrong turn! The page you're looking for doesn't exist. 
            Let's get you back on the right path to optimize your traffic routes.
        </p>
        <div class="error-actions">
            <a href="/" class="btn btn-primary">
                <i class="fas fa-home"></i>
                Go Home
            </a>
            <a href="/auth.html" class="btn btn-secondary">
                <i class="fas fa-sign-in-alt"></i>
                Sign In
            </a>
        </div>
    </div>

    <script>
        // Auto-redirect to home page after 10 seconds
        setTimeout(() => {
            window.location.href = '/';
        }, 10000);
    </script>
</body>
</html>
