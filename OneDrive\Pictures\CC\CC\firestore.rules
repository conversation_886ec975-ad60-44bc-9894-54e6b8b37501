rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Search History Rules
    match /searchHistory/{document} {
      // Users can read and write their own search history
      allow read, write: if request.auth != null && 
                        request.auth.uid == resource.data.userId;
      
      // Allow creation if the userId matches the authenticated user
      allow create: if request.auth != null && 
                   request.auth.uid == request.resource.data.userId;
    }
    
    // User Profiles (if needed in the future)
    match /users/{userId} {
      allow read, write: if request.auth != null && 
                        request.auth.uid == userId;
    }
    
    // Default deny all other access
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
