/* Authentication Page Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    overflow-x: hidden;
}

.auth-container {
    min-height: 100vh;
    position: relative;
    display: flex;
    flex-direction: column;
}

/* Background Elements */
.auth-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    overflow: hidden;
}

.bg-shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 6s ease-in-out infinite;
}

.shape-1 {
    width: 200px;
    height: 200px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.shape-3 {
    width: 100px;
    height: 100px;
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* Header */
.auth-header {
    padding: 2rem;
    z-index: 10;
}

.logo {
    display: flex;
    align-items: center;
    gap: 1rem;
    text-decoration: none;
    color: white;
    transition: transform 0.3s ease;
}

.logo:hover {
    transform: translateY(-2px);
}

.logo-icon {
    font-size: 2rem;
    color: #ffd700;
}

.brand-text span {
    font-size: 1.5rem;
    font-weight: 700;
    display: block;
}

.brand-text small {
    font-size: 0.9rem;
    opacity: 0.8;
    font-weight: 400;
}

/* Main Content */
.auth-main {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    gap: 4rem;
}

.auth-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 3rem;
    width: 100%;
    max-width: 450px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.auth-form {
    display: none;
}

.auth-form.active {
    display: block;
}

.form-header {
    text-align: center;
    margin-bottom: 2rem;
}

.form-header h2 {
    color: #2d3748;
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.form-header p {
    color: #718096;
    font-size: 0.95rem;
}

/* Form Elements */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: #2d3748;
    font-weight: 500;
    font-size: 0.9rem;
}

.input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.input-wrapper i {
    position: absolute;
    left: 1rem;
    color: #a0aec0;
    z-index: 2;
}

.input-wrapper input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    background: white;
}

.input-wrapper input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.toggle-password {
    position: absolute;
    right: 1rem;
    background: none;
    border: none;
    color: #a0aec0;
    cursor: pointer;
    padding: 0.25rem;
    z-index: 2;
}

/* Buttons */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 10px;
    font-size: 0.95rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
}

.btn-google {
    background: white;
    color: #2d3748;
    border: 2px solid #e2e8f0;
}

.btn-google:hover {
    background: #f7fafc;
    border-color: #cbd5e0;
}

.btn-full {
    width: 100%;
}

.btn-outline {
    background: transparent;
    border: 2px solid #e2e8f0;
    color: #2d3748;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
}

/* Form Options */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    font-size: 0.9rem;
}

.checkbox-wrapper {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
}

.forgot-password {
    color: #667eea;
    text-decoration: none;
}

.forgot-password:hover {
    text-decoration: underline;
}

/* Divider */
.divider {
    text-align: center;
    margin: 1.5rem 0;
    position: relative;
    color: #a0aec0;
    font-size: 0.9rem;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e2e8f0;
    z-index: 1;
}

.divider span {
    background: white;
    padding: 0 1rem;
    position: relative;
    z-index: 2;
}

/* Form Footer */
.form-footer {
    text-align: center;
    margin-top: 1.5rem;
    font-size: 0.9rem;
    color: #718096;
}

.form-footer a {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
}

.form-footer a:hover {
    text-decoration: underline;
}

/* Features Section */
.auth-features {
    max-width: 500px;
    color: white;
}

.auth-features h3 {
    font-size: 1.5rem;
    margin-bottom: 2rem;
    text-align: center;
}

.features-grid {
    display: grid;
    gap: 1.5rem;
}

.feature-item {
    display: flex;
    gap: 1rem;
    align-items: flex-start;
}

.feature-icon {
    font-size: 1.5rem;
    color: #ffd700;
    margin-top: 0.25rem;
}

.feature-content h4 {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.feature-content p {
    font-size: 0.9rem;
    opacity: 0.9;
    line-height: 1.5;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-spinner {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    color: #2d3748;
}

.loading-spinner i {
    font-size: 2rem;
    color: #667eea;
    margin-bottom: 1rem;
}

/* Notifications */
.auth-notification {
    position: fixed;
    top: 2rem;
    right: 2rem;
    padding: 1rem 1.5rem;
    border-radius: 10px;
    color: white;
    font-weight: 500;
    z-index: 1001;
    animation: slideIn 0.3s ease;
}

.auth-notification.success {
    background: #48bb78;
}

.auth-notification.error {
    background: #f56565;
}

@keyframes slideIn {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

/* Responsive Design */
@media (max-width: 768px) {
    .auth-main {
        flex-direction: column;
        gap: 2rem;
    }
    
    .auth-card {
        padding: 2rem;
    }
    
    .auth-features {
        order: -1;
        text-align: center;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
}
