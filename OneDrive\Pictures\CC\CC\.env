# Google Maps API Configuration
GOOGLE_MAPS_API_KEY=AIzaSyDdGyIXH74Bk53kKtMyC4dCnOyVdYy8hM4

# Google Gemini AI Configuration
GEMINI_API_KEY=AIzaSyAWvkbvnuLGXFLiuVkaUg-l0ommE7x66Io

# Google Cloud Configuration
GOOGLE_CLOUD_PROJECT_ID=esoteric-kiln-459609-t9
GOOGLE_APPLICATION_CREDENTIALS=service-account-key.json

# Application Configuration
DEBUG=True
HOST=0.0.0.0
PORT=8080

# ML Model Configuration
MODEL_PATH=data/traffic_model.pkl
TRAINING_DATA_PATH=data/sample_traffic_data.csv

# BigQuery Configuration
BIGQUERY_DATASET_ID=traffic_data
BIGQUERY_TABLE_ID=traffic_logs

# Real Data API Keys
OPENWEATHER_API_KEY=********************************
TOMTOM_API_KEY=********************************
WEATHERAPI_KEY=6b925b6c91f34642bb2192752251806
TRANSITLAND_API_KEY=qC1rRXxkU4oqIY0ONplbDbfqi3h9WfQ4
