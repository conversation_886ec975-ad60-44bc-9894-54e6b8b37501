"""
Firestore Database Service for SmartCity AI
Handles all database operations for traffic data, user preferences, and chat history
"""

import os
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from google.cloud import firestore
from google.oauth2 import service_account
from backend.utils.config import config

logger = logging.getLogger(__name__)

class FirestoreService:
    """Service for Firestore database operations."""
    
    def __init__(self):
        """Initialize Firestore client."""
        self.db = None
        self._initialize_client()
    
    def _initialize_client(self):
        """Initialize Firestore client with proper authentication."""
        try:
            # Try to use service account key if available
            if os.path.exists('service-account-key.json'):
                credentials = service_account.Credentials.from_service_account_file(
                    'service-account-key.json'
                )
                self.db = firestore.Client(
                    credentials=credentials,
                    project=config.GOOGLE_CLOUD_PROJECT_ID
                )
                logger.info("Firestore initialized with service account key")
            else:
                # Use default credentials (for Cloud Run or local gcloud auth)
                self.db = firestore.Client(project=config.GOOGLE_CLOUD_PROJECT_ID)
                logger.info("Firestore initialized with default credentials")
                
        except Exception as e:
            logger.error(f"Failed to initialize Firestore: {e}")
            self.db = None
    
    def is_available(self) -> bool:
        """Check if Firestore is available."""
        return self.db is not None
    
    # === TRAFFIC DATA OPERATIONS ===
    
    async def save_traffic_data(self, location: str, coordinates: Dict[str, float], 
                               traffic_level: str, congestion_score: float, 
                               average_speed: float, weather_condition: str = None) -> str:
        """Save traffic data to Firestore."""
        if not self.is_available():
            logger.warning("Firestore not available, skipping traffic data save")
            return None
            
        try:
            traffic_data = {
                'location': location,
                'coordinates': coordinates,
                'traffic_level': traffic_level,
                'congestion_score': congestion_score,
                'average_speed': average_speed,
                'weather_condition': weather_condition,
                'timestamp': datetime.now(),
                'rush_hour': self._is_rush_hour()
            }
            
            doc_ref = self.db.collection('traffic_data').document()
            doc_ref.set(traffic_data)
            
            logger.info(f"Traffic data saved for location: {location}")
            return doc_ref.id
            
        except Exception as e:
            logger.error(f"Error saving traffic data: {e}")
            return None
    
    async def get_recent_traffic_data(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent traffic data from Firestore."""
        if not self.is_available():
            return []
            
        try:
            docs = (self.db.collection('traffic_data')
                   .order_by('timestamp', direction=firestore.Query.DESCENDING)
                   .limit(limit)
                   .stream())
            
            traffic_data = []
            for doc in docs:
                data = doc.to_dict()
                data['id'] = doc.id
                traffic_data.append(data)
            
            return traffic_data
            
        except Exception as e:
            logger.error(f"Error getting traffic data: {e}")
            return []
    
    # === USER PREFERENCES OPERATIONS ===
    
    async def save_user_preferences(self, user_id: str, preferences: Dict[str, Any]) -> bool:
        """Save user preferences to Firestore."""
        if not self.is_available():
            return False
            
        try:
            preferences['updated_at'] = datetime.now()
            if 'created_at' not in preferences:
                preferences['created_at'] = datetime.now()
            
            self.db.collection('user_preferences').document(user_id).set(
                preferences, merge=True
            )
            
            logger.info(f"User preferences saved for user: {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving user preferences: {e}")
            return False
    
    async def get_user_preferences(self, user_id: str) -> Dict[str, Any]:
        """Get user preferences from Firestore."""
        if not self.is_available():
            return self._get_default_preferences()
            
        try:
            doc = self.db.collection('user_preferences').document(user_id).get()
            
            if doc.exists:
                return doc.to_dict()
            else:
                # Return default preferences for new users
                return self._get_default_preferences()
                
        except Exception as e:
            logger.error(f"Error getting user preferences: {e}")
            return self._get_default_preferences()
    
    def _get_default_preferences(self) -> Dict[str, Any]:
        """Get default user preferences."""
        return {
            'preferred_route_type': 'fastest',
            'avoid_tolls': False,
            'avoid_highways': False,
            'eco_mode': True,
            'notifications_enabled': True,
            'language': 'en',
            'created_at': datetime.now(),
            'updated_at': datetime.now()
        }
    
    # === ROUTE HISTORY OPERATIONS ===
    
    async def save_route_history(self, user_id: str, route_data: Dict[str, Any]) -> str:
        """Save route history to Firestore."""
        if not self.is_available():
            return None
            
        try:
            route_data['user_id'] = user_id
            route_data['timestamp'] = datetime.now()
            
            doc_ref = self.db.collection('route_history').document()
            doc_ref.set(route_data)
            
            logger.info(f"Route history saved for user: {user_id}")
            return doc_ref.id
            
        except Exception as e:
            logger.error(f"Error saving route history: {e}")
            return None
    
    async def get_user_route_history(self, user_id: str, limit: int = 20) -> List[Dict[str, Any]]:
        """Get user's route history from Firestore."""
        if not self.is_available():
            return []
            
        try:
            docs = (self.db.collection('route_history')
                   .where('user_id', '==', user_id)
                   .order_by('timestamp', direction=firestore.Query.DESCENDING)
                   .limit(limit)
                   .stream())
            
            history = []
            for doc in docs:
                data = doc.to_dict()
                data['id'] = doc.id
                history.append(data)
            
            return history
            
        except Exception as e:
            logger.error(f"Error getting route history: {e}")
            return []
    
    # === CHAT HISTORY OPERATIONS ===
    
    async def save_chat_message(self, user_id: str, session_id: str, 
                               role: str, content: str) -> bool:
        """Save chat message to Firestore."""
        if not self.is_available():
            return False
            
        try:
            # Get or create chat session document
            chat_ref = self.db.collection('chat_history').document(session_id)
            chat_doc = chat_ref.get()
            
            message = {
                'role': role,
                'content': content,
                'timestamp': datetime.now()
            }
            
            if chat_doc.exists:
                # Append to existing messages
                chat_ref.update({
                    'messages': firestore.ArrayUnion([message]),
                    'updated_at': datetime.now()
                })
            else:
                # Create new chat session
                chat_data = {
                    'user_id': user_id,
                    'session_id': session_id,
                    'messages': [message],
                    'created_at': datetime.now(),
                    'updated_at': datetime.now()
                }
                chat_ref.set(chat_data)
            
            logger.info(f"Chat message saved for session: {session_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving chat message: {e}")
            return False
    
    async def get_chat_history(self, session_id: str) -> Dict[str, Any]:
        """Get chat history for a session."""
        if not self.is_available():
            return {'messages': []}
            
        try:
            doc = self.db.collection('chat_history').document(session_id).get()
            
            if doc.exists:
                return doc.to_dict()
            else:
                return {'messages': []}
                
        except Exception as e:
            logger.error(f"Error getting chat history: {e}")
            return {'messages': []}
    
    # === SYSTEM CONFIGURATION ===
    
    async def get_system_config(self) -> Dict[str, Any]:
        """Get system configuration from Firestore."""
        if not self.is_available():
            return self._get_default_config()
            
        try:
            doc = self.db.collection('system_config').document('app_config').get()
            
            if doc.exists:
                return doc.to_dict()
            else:
                return self._get_default_config()
                
        except Exception as e:
            logger.error(f"Error getting system config: {e}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default system configuration."""
        return {
            'app_version': '2.0.0',
            'api_version': 'v1',
            'maintenance_mode': False,
            'features': {
                'route_optimization': True,
                'eco_chatbot': True,
                'real_time_traffic': True,
                'weather_integration': True
            },
            'limits': {
                'max_routes_per_day': 100,
                'max_chat_messages_per_session': 50
            }
        }
    
    # === UTILITY METHODS ===
    
    def _is_rush_hour(self) -> bool:
        """Check if current time is rush hour."""
        now = datetime.now()
        hour = now.hour
        
        # Morning rush: 7-10 AM, Evening rush: 5-8 PM
        return (7 <= hour <= 10) or (17 <= hour <= 20)

# Global Firestore service instance
firestore_service = FirestoreService()
