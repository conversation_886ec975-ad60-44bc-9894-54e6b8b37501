# 🚀 SmartCity AI Deployment Guide

This guide covers multiple deployment options for the SmartCity AI Traffic Optimization System.

## 📋 Prerequisites

- All API keys configured in your environment
- Firebase project set up and configured
- Service account key file (if using Google Cloud services)

## 🎯 Deployment Options

### Option 1: Google Cloud Run (Recommended)

**Pros:** Best integration with Google services, scalable, pay-per-use
**Cost:** Free tier available, then pay-per-request

#### Steps:
1. Install Google Cloud SDK
2. Authenticate: `gcloud auth login`
3. Run deployment script: `./deploy_cloudrun.sh`

#### Manual Deployment:
```bash
# Set project
gcloud config set project smart-traffic-511c5

# Build and deploy
gcloud run deploy smartcity-ai \
  --source . \
  --region us-central1 \
  --allow-unauthenticated \
  --port 8080 \
  --memory 2Gi \
  --set-env-vars "GOOGLE_MAPS_API_KEY=your_key,GEMINI_API_KEY=your_key"
```

### Option 2: Railway

**Pros:** Simple deployment, good free tier, automatic HTTPS
**Cost:** Free tier available

#### Steps:
1. Create account at [railway.app](https://railway.app)
2. Connect your GitHub repository
3. Add environment variables in Railway dashboard
4. Deploy automatically

#### Environment Variables to Set:
```
GOOGLE_MAPS_API_KEY=AIzaSyDdGyIXH74Bk53kKtMyC4dCnOyVdYy8hM4
GEMINI_API_KEY=AIzaSyAWvkbvnuLGXFLiuVkaUg-l0ommE7x66Io
GOOGLE_CLOUD_PROJECT_ID=smart-traffic-511c5
DEBUG=False
HOST=0.0.0.0
PORT=8080
OPENWEATHER_API_KEY=********************************
TOMTOM_API_KEY=********************************
WEATHERAPI_KEY=6b925b6c91f34642bb2192752251806
TRANSITLAND_API_KEY=qC1rRXxkU4oqIY0ONplbDbfqi3h9WfQ4
```

### Option 3: Render

**Pros:** Free tier, easy deployment, automatic SSL
**Cost:** Free tier available

#### Steps:
1. Create account at [render.com](https://render.com)
2. Connect GitHub repository
3. Choose "Web Service"
4. Set build command: `pip install -r requirements.txt`
5. Set start command: `python -m backend.main`
6. Add environment variables

### Option 4: Heroku

**Pros:** Popular platform, good documentation
**Cost:** No free tier (starts at $5/month)

#### Steps:
1. Install Heroku CLI
2. Create Procfile: `web: python -m backend.main`
3. Deploy: `git push heroku main`

## 🔧 Environment Variables

Make sure to set these environment variables in your deployment platform:

```env
GOOGLE_MAPS_API_KEY=your_google_maps_key
GEMINI_API_KEY=your_gemini_key
GOOGLE_CLOUD_PROJECT_ID=smart-traffic-511c5
DEBUG=False
HOST=0.0.0.0
PORT=8080
OPENWEATHER_API_KEY=your_openweather_key
TOMTOM_API_KEY=your_tomtom_key
WEATHERAPI_KEY=your_weather_key
TRANSITLAND_API_KEY=your_transitland_key
```

## 🔒 Security Notes

- Never commit API keys to version control
- Use environment variables for all sensitive data
- Enable HTTPS (most platforms do this automatically)
- Consider setting up authentication for admin features

## 📊 Monitoring

After deployment, monitor your application:
- Check application logs
- Monitor API usage and costs
- Set up alerts for errors
- Monitor performance metrics

## 🆘 Troubleshooting

Common issues and solutions:
- **Port binding errors**: Ensure PORT environment variable is set
- **API key errors**: Verify all environment variables are set correctly
- **Firebase errors**: Check service account key and project configuration
- **Memory issues**: Increase memory allocation in deployment settings

## 📞 Support

If you encounter issues:
1. Check the application logs
2. Verify environment variables
3. Test API keys individually
4. Check Firebase project configuration
