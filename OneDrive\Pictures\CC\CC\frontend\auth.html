<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign In - SmartCity AI</title>
    
    <!-- Stylesheets -->
    <link rel="stylesheet" href="static/css/auth.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Ctext y='.9em' font-size='90'%3E🚦%3C/text%3E%3C/svg%3E">
</head>
<body>
    <div class="auth-container">
        <!-- Background Elements -->
        <div class="auth-background">
            <div class="bg-shape shape-1"></div>
            <div class="bg-shape shape-2"></div>
            <div class="bg-shape shape-3"></div>
        </div>
        
        <!-- Header -->
        <header class="auth-header">
            <a href="/" class="logo">
                <div class="logo-icon">
                    <i class="fas fa-traffic-light"></i>
                </div>
                <div class="brand-text">
                    <span>SmartCity AI</span>
                    <small>Traffic Optimization System</small>
                </div>
            </a>
        </header>
        
        <!-- Main Auth Content -->
        <main class="auth-main">
            <div class="auth-card">
                <!-- Sign In Form -->
                <div class="auth-form active" id="signin-form">
                    <div class="form-header">
                        <h2>Welcome Back</h2>
                        <p>Sign in to access your personalized traffic dashboard</p>
                    </div>
                    
                    <form id="signin-form-element">
                        <div class="form-group">
                            <label for="signin-email">Email Address</label>
                            <div class="input-wrapper">
                                <i class="fas fa-envelope"></i>
                                <input type="email" id="signin-email" name="email" required 
                                       placeholder="Enter your email address">
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="signin-password">Password</label>
                            <div class="input-wrapper">
                                <i class="fas fa-lock"></i>
                                <input type="password" id="signin-password" name="password" required 
                                       placeholder="Enter your password">
                                <button type="button" class="toggle-password" data-target="signin-password">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="form-options">
                            <label class="checkbox-wrapper">
                                <input type="checkbox" id="remember-me">
                                <span class="checkmark"></span>
                                Remember me
                            </label>
                            <a href="#" class="forgot-password">Forgot password?</a>
                        </div>
                        
                        <button type="submit" class="btn btn-primary btn-full">
                            <i class="fas fa-sign-in-alt"></i>
                            Sign In
                        </button>
                    </form>
                    
                    <div class="divider">
                        <span>or continue with</span>
                    </div>
                    
                    <button id="google-signin" class="btn btn-google btn-full">
                        <i class="fab fa-google"></i>
                        Sign in with Google
                    </button>
                    
                    <div class="form-footer">
                        <p>Don't have an account? 
                            <a href="#" id="show-signup">Create one now</a>
                        </p>
                    </div>
                </div>
                
                <!-- Sign Up Form -->
                <div class="auth-form" id="signup-form">
                    <div class="form-header">
                        <h2>Create Account</h2>
                        <p>Join SmartCity AI for personalized traffic optimization</p>
                    </div>
                    
                    <form id="signup-form-element">
                        <div class="form-group">
                            <label for="signup-name">Full Name</label>
                            <div class="input-wrapper">
                                <i class="fas fa-user"></i>
                                <input type="text" id="signup-name" name="name" required 
                                       placeholder="Enter your full name">
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="signup-email">Email Address</label>
                            <div class="input-wrapper">
                                <i class="fas fa-envelope"></i>
                                <input type="email" id="signup-email" name="email" required 
                                       placeholder="Enter your email address">
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="signup-password">Password</label>
                            <div class="input-wrapper">
                                <i class="fas fa-lock"></i>
                                <input type="password" id="signup-password" name="password" required 
                                       placeholder="Create a strong password" minlength="6">
                                <button type="button" class="toggle-password" data-target="signup-password">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="password-strength" id="password-strength"></div>
                        </div>
                        
                        <div class="form-group">
                            <label for="confirm-password">Confirm Password</label>
                            <div class="input-wrapper">
                                <i class="fas fa-lock"></i>
                                <input type="password" id="confirm-password" name="confirmPassword" required 
                                       placeholder="Confirm your password">
                            </div>
                        </div>
                        
                        <div class="form-options">
                            <label class="checkbox-wrapper">
                                <input type="checkbox" id="agree-terms" required>
                                <span class="checkmark"></span>
                                I agree to the <a href="#" target="_blank">Terms of Service</a> and 
                                <a href="#" target="_blank">Privacy Policy</a>
                            </label>
                        </div>
                        
                        <button type="submit" class="btn btn-primary btn-full">
                            <i class="fas fa-user-plus"></i>
                            Create Account
                        </button>
                    </form>
                    
                    <div class="divider">
                        <span>or continue with</span>
                    </div>
                    
                    <button id="google-signup" class="btn btn-google btn-full">
                        <i class="fab fa-google"></i>
                        Sign up with Google
                    </button>
                    
                    <div class="form-footer">
                        <p>Already have an account? 
                            <a href="#" id="show-signin">Sign in here</a>
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- Features Section -->
            <div class="auth-features">
                <h3>Why Choose SmartCity AI?</h3>
                <div class="features-grid">
                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="fas fa-route"></i>
                        </div>
                        <div class="feature-content">
                            <h4>Smart Routing</h4>
                            <p>AI-powered route optimization for faster, more efficient travel</p>
                        </div>
                    </div>
                    
                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="fas fa-leaf"></i>
                        </div>
                        <div class="feature-content">
                            <h4>Eco-Friendly</h4>
                            <p>Reduce your carbon footprint with environmentally conscious routing</p>
                        </div>
                    </div>
                    
                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="fas fa-history"></i>
                        </div>
                        <div class="feature-content">
                            <h4>Search History</h4>
                            <p>Access your route history across all devices, anytime</p>
                        </div>
                    </div>
                    
                    <div class="feature-item">
                        <div class="feature-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="feature-content">
                            <h4>Analytics</h4>
                            <p>Track your travel patterns and environmental impact</p>
                        </div>
                    </div>
                </div>
            </div>
        </main>
        
        <!-- Loading Overlay -->
        <div class="loading-overlay" id="loading-overlay">
            <div class="loading-spinner">
                <i class="fas fa-spinner fa-spin"></i>
                <p>Processing...</p>
            </div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script type="module" src="static/js/firebase-config.js"></script>
    <script type="module" src="static/js/auth.js"></script>
    <script type="module">
        import authManager from 'static/js/auth.js';
        
        // Initialize auth page functionality
        document.addEventListener('DOMContentLoaded', () => {
            initAuthPage();
        });
        
        function initAuthPage() {
            // Form switching
            const showSignup = document.getElementById('show-signup');
            const showSignin = document.getElementById('show-signin');
            const signinForm = document.getElementById('signin-form');
            const signupForm = document.getElementById('signup-form');
            
            showSignup?.addEventListener('click', (e) => {
                e.preventDefault();
                signinForm.classList.remove('active');
                signupForm.classList.add('active');
            });
            
            showSignin?.addEventListener('click', (e) => {
                e.preventDefault();
                signupForm.classList.remove('active');
                signinForm.classList.add('active');
            });
            
            // Password visibility toggle
            document.querySelectorAll('.toggle-password').forEach(btn => {
                btn.addEventListener('click', () => {
                    const targetId = btn.dataset.target;
                    const input = document.getElementById(targetId);
                    const icon = btn.querySelector('i');
                    
                    if (input.type === 'password') {
                        input.type = 'text';
                        icon.classList.replace('fa-eye', 'fa-eye-slash');
                    } else {
                        input.type = 'password';
                        icon.classList.replace('fa-eye-slash', 'fa-eye');
                    }
                });
            });
            
            // Form submissions
            document.getElementById('signin-form-element')?.addEventListener('submit', handleSignIn);
            document.getElementById('signup-form-element')?.addEventListener('submit', handleSignUp);
            
            // Google auth buttons
            document.getElementById('google-signin')?.addEventListener('click', handleGoogleAuth);
            document.getElementById('google-signup')?.addEventListener('click', handleGoogleAuth);
            
            // Check if user is already signed in
            authManager.addAuthStateListener((user) => {
                if (user) {
                    // Show success message and redirect to dashboard immediately
                    showLoading(false);
                    console.log('User authenticated, redirecting to dashboard');
                    window.location.href = 'dashboard.html';
                }
            });

            // Check current auth state immediately
            setTimeout(() => {
                const currentUser = authManager.getCurrentUser();
                if (currentUser) {
                    console.log('User already authenticated, redirecting to dashboard');
                    window.location.href = 'dashboard.html';
                }
            }, 500);
        }
        
        async function handleSignIn(e) {
            e.preventDefault();
            showLoading(true);
            
            const email = document.getElementById('signin-email').value;
            const password = document.getElementById('signin-password').value;
            
            try {
                await authManager.signIn(email, password);
                // Redirect will happen automatically via auth state listener
            } catch (error) {
                console.error('Sign in error:', error);
            } finally {
                showLoading(false);
            }
        }
        
        async function handleSignUp(e) {
            e.preventDefault();
            showLoading(true);
            
            const name = document.getElementById('signup-name').value;
            const email = document.getElementById('signup-email').value;
            const password = document.getElementById('signup-password').value;
            const confirmPassword = document.getElementById('confirm-password').value;
            
            if (password !== confirmPassword) {
                authManager.showError('Passwords do not match');
                showLoading(false);
                return;
            }
            
            try {
                await authManager.signUp(email, password, name);
                // Redirect will happen automatically via auth state listener
            } catch (error) {
                console.error('Sign up error:', error);
            } finally {
                showLoading(false);
            }
        }
        
        async function handleGoogleAuth() {
            showLoading(true);
            
            try {
                await authManager.signInWithGoogle();
                // Redirect will happen automatically via auth state listener
            } catch (error) {
                console.error('Google auth error:', error);
            } finally {
                showLoading(false);
            }
        }
        
        function showLoading(show) {
            const overlay = document.getElementById('loading-overlay');
            if (overlay) {
                overlay.style.display = show ? 'flex' : 'none';
            }
        }

        function showSuccessMessage(message) {
            const notification = document.createElement('div');
            notification.className = 'auth-notification success';
            notification.innerHTML = `
                <div class="notification-content">
                    <i class="fas fa-check-circle"></i>
                    <span>${message}</span>
                </div>
            `;
            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 3000);
        }
    </script>
</body>
</html>
