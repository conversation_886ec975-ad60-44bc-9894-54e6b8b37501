<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Auth - SmartCity AI</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .debug-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        input {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .form-group {
            margin: 15px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>🚦 SmartCity AI - Authentication Debug</h1>
        
        <div id="status-container">
            <div class="status info">
                <strong>Status:</strong> Initializing Firebase...
            </div>
        </div>

        <div id="firebase-status">
            <h3>Firebase Configuration Status</h3>
            <div id="firebase-config-status">Checking...</div>
        </div>

        <div id="auth-section" style="display: none;">
            <h3>Authentication Test</h3>
            
            <div class="form-group">
                <label for="test-email">Email:</label>
                <input type="email" id="test-email" placeholder="<EMAIL>" value="<EMAIL>">
            </div>
            
            <div class="form-group">
                <label for="test-password">Password:</label>
                <input type="password" id="test-password" placeholder="password" value="akhil123">
            </div>
            
            <button onclick="testSignUp()">Test Sign Up</button>
            <button onclick="testSignIn()">Test Sign In</button>
            <button onclick="testGoogleAuth()">Test Google Auth</button>
            <button onclick="testSignOut()">Sign Out</button>
        </div>

        <div id="user-info" style="display: none;">
            <h3>Current User</h3>
            <div id="user-details"></div>
        </div>

        <div id="logs">
            <h3>Debug Logs</h3>
            <div id="log-container" style="background: #f8f9fa; padding: 10px; border-radius: 4px; max-height: 300px; overflow-y: auto;"></div>
        </div>
    </div>

    <script type="module">
        // Import Firebase modules
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { 
            getAuth, 
            signInWithEmailAndPassword, 
            createUserWithEmailAndPassword, 
            signInWithPopup, 
            GoogleAuthProvider, 
            signOut, 
            onAuthStateChanged 
        } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyDsBFGpYsnNrIv0owBnbueYd5DMB8jfh5k",
            authDomain: "smart-traffic-511c5.firebaseapp.com",
            projectId: "smart-traffic-511c5",
            storageBucket: "smart-traffic-511c5.firebasestorage.app",
            messagingSenderId: "810928629643",
            appId: "1:810928629643:web:589476964df58011af718e",
            measurementId: "G-JGKYJZ46EB"
        };

        let app, auth, googleProvider;

        // Logging function
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logContainer = document.getElementById('log-container');
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            logEntry.style.color = type === 'error' ? 'red' : type === 'success' ? 'green' : 'black';
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }

        function updateStatus(message, type = 'info') {
            const statusContainer = document.getElementById('status-container');
            statusContainer.innerHTML = `<div class="status ${type}"><strong>Status:</strong> ${message}</div>`;
        }

        // Initialize Firebase
        try {
            log('Initializing Firebase...');
            app = initializeApp(firebaseConfig);
            auth = getAuth(app);
            googleProvider = new GoogleAuthProvider();
            
            log('Firebase initialized successfully', 'success');
            updateStatus('Firebase initialized successfully', 'success');
            
            document.getElementById('firebase-config-status').innerHTML = `
                <div class="status success">
                    ✅ Firebase App: Initialized<br>
                    ✅ Auth: Ready<br>
                    ✅ Project ID: ${firebaseConfig.projectId}<br>
                    ✅ Auth Domain: ${firebaseConfig.authDomain}
                </div>
            `;
            
            document.getElementById('auth-section').style.display = 'block';
            
        } catch (error) {
            log(`Firebase initialization error: ${error.message}`, 'error');
            updateStatus(`Firebase initialization failed: ${error.message}`, 'error');
            document.getElementById('firebase-config-status').innerHTML = `
                <div class="status error">❌ Firebase initialization failed: ${error.message}</div>
            `;
        }

        // Auth state listener
        if (auth) {
            onAuthStateChanged(auth, (user) => {
                if (user) {
                    log(`User signed in: ${user.email}`, 'success');
                    updateStatus(`Signed in as ${user.email}`, 'success');
                    document.getElementById('user-info').style.display = 'block';
                    document.getElementById('user-details').innerHTML = `
                        <div class="status success">
                            <strong>Email:</strong> ${user.email}<br>
                            <strong>Display Name:</strong> ${user.displayName || 'Not set'}<br>
                            <strong>UID:</strong> ${user.uid}<br>
                            <strong>Email Verified:</strong> ${user.emailVerified}
                        </div>
                    `;
                } else {
                    log('User signed out');
                    updateStatus('Not signed in', 'info');
                    document.getElementById('user-info').style.display = 'none';
                }
            });
        }

        // Test functions
        window.testSignUp = async function() {
            const email = document.getElementById('test-email').value;
            const password = document.getElementById('test-password').value;
            
            if (!email || !password) {
                log('Please enter email and password', 'error');
                return;
            }
            
            try {
                log(`Attempting to sign up with email: ${email}`);
                const userCredential = await createUserWithEmailAndPassword(auth, email, password);
                log(`Sign up successful: ${userCredential.user.email}`, 'success');
            } catch (error) {
                log(`Sign up error: ${error.code} - ${error.message}`, 'error');
            }
        };

        window.testSignIn = async function() {
            const email = document.getElementById('test-email').value;
            const password = document.getElementById('test-password').value;
            
            if (!email || !password) {
                log('Please enter email and password', 'error');
                return;
            }
            
            try {
                log(`Attempting to sign in with email: ${email}`);
                const userCredential = await signInWithEmailAndPassword(auth, email, password);
                log(`Sign in successful: ${userCredential.user.email}`, 'success');
            } catch (error) {
                log(`Sign in error: ${error.code} - ${error.message}`, 'error');
            }
        };

        window.testGoogleAuth = async function() {
            try {
                log('Attempting Google sign in...');
                const result = await signInWithPopup(auth, googleProvider);
                log(`Google sign in successful: ${result.user.email}`, 'success');
            } catch (error) {
                log(`Google sign in error: ${error.code} - ${error.message}`, 'error');
            }
        };

        window.testSignOut = async function() {
            try {
                log('Attempting to sign out...');
                await signOut(auth);
                log('Sign out successful', 'success');
            } catch (error) {
                log(`Sign out error: ${error.message}`, 'error');
            }
        };

        log('Debug page loaded successfully');
    </script>
</body>
</html>
